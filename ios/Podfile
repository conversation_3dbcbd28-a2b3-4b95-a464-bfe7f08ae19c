# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, '15.4'
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'ModrkClient' do
  config = use_native_modules!

  # React Native Modules
  pod 'react-native-blob-util', :path => '../node_modules/react-native-blob-util'
  pod 'react-native-maps', :path => '../node_modules/react-native-maps'
  pod 'react-native-vlc-media-player', :path => '../node_modules/react-native-vlc-media-player'

  # Permissions
  permissions_path = '../node_modules/react-native-permissions/ios'
  pod 'Permission-LocationAlways', :path => "#{permissions_path}/LocationAlways/Permission-LocationAlways.podspec"
  pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse/Permission-LocationWhenInUse.podspec"
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera/Permission-Camera.podspec"
  pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications/Permission-Notifications.podspec"

  # Google Maps
  pod 'GoogleMaps'
  pod 'Google-Maps-iOS-Utils'

  # Firebase (modular headers)
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true

  use_react_native!(
    :path => config[:reactNativePath],
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'ModrkClientTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # React Native required post-install
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )

    # Force iOS 15.4 deployment target for all pods
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.4'
      end
    end
  end
end
