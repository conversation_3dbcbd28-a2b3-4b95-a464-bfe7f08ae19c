<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>ModrkClient</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array />
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<true />
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbapi20160328</string>
			<string>fbauth</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
			<string>fb-messenger-api</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
			<key>NSAllowsArbitraryLoads</key>
			<false />
			<key>NSAllowsLocalNetworking</key>
			<true />
		</dict>
		<key>NSCameraUsageDescription</key>
		<string>Modrk wants to access your camera to upload your profile image</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Modrk wants to access your location to attach your order with it</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Modrk wants to access your location to attach your order with it</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Modrk wants to access your location to attach your order with it</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Allow microphone to upload audios</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Modrk wants to access your camera to upload your profile image</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Modrk wants to access your camera to upload your profile image</string>
		<key>UIAppFonts</key>
		<array>
			<string>Cairo-Bold.ttf</string>
			<string>Cairo-Medium.ttf</string>
			<string>Cairo-Regular.ttf</string>
			<string>AntDesign.ttf</string>
			<string>Entypo.ttf</string>
			<string>EvilIcons.ttf</string>
			<string>Feather.ttf</string>
			<string>FontAwesome.ttf</string>
			<string>FontAwesome5_Brands.ttf</string>
			<string>FontAwesome5_Regular.ttf</string>
			<string>FontAwesome5_Solid.ttf</string>
			<string>Fontisto.ttf</string>
			<string>Foundation.ttf</string>
			<string>Ionicons.ttf</string>
			<string>MaterialCommunityIcons.ttf</string>
			<string>MaterialIcons.ttf</string>
			<string>Octicons.ttf</string>
			<string>Roboto_medium.ttf</string>
			<string>Roboto.ttf</string>
			<string>rubicon-icon-font.ttf</string>
			<string>SimpleLineIcons.ttf</string>
			<string>Zocial.ttf</string>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
	</dict>
</plist>