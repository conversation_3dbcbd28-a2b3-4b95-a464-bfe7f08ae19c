buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "25.2.9519653"
        kotlinVersion = "1.9.22"
        googlePlayServicesVersion = '+'
        firebaseMessagingVersion = '21.1.0'
        firebaseVersion = '17.3.4'
        androidXCore = "1.7.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.6.0")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("de.undercouch:gradle-download-task:4.1.2")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
        classpath("com.google.gms:google-services:4.3.13")
    }
}

apply plugin: "com.facebook.react.rootproject"

allprojects {
    repositories {
        exclusiveContent {
            filter {
                includeGroup "com.facebook.react"
            }
            forRepository {
                maven {
                    url "$rootDir/../node_modules/react-native/android"
                }
            }
        }
        maven {
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        maven {
            url 'https://www.jitpack.io'
        }
        google()
        mavenCentral()
    }
}
