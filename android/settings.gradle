rootProject.name = 'ModrkClient'
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':app'
includeBuild('../node_modules/react-native/node_modules/@react-native/gradle-plugin')

include ':react-native-vlc-media-player'
project(':react-native-vlc-media-player').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-vlc-media-player/android')

include ':react-native-vlc-rtsp'
project(':react-native-vlc-rtsp').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-vlc-rtsp/android')

include ':react-native-audio-recorder-player'
project(':react-native-audio-recorder-player').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-audio-recorder-player/android')

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)

    repositories {
        google()
        mavenCentral()
        maven { url("$rootDir/../node_modules/react-native/android") }
        maven { url 'https://www.jitpack.io' }
    }
}
